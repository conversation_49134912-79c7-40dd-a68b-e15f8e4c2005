'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import styles from './AIReadinessBody.module.css';
import useMediaQueryState from '@hooks/useMediaQueryState';

import QuestionAndAnswers from '@components/QuestionAndAnswers';
import Heading from '@components/Heading';
import Button from '@components/Button';
import HeroSection from '@components/HeroSection';
import AIReadinessStep from '@components/AIReadinessStep';
import RatingGauge from '@components/RatingGuage';
import RatingProgressCircle from '@components/RatingProgressCircle';
import AIReadinessForm from '@components/AIReadinessForm';

export default function AIReadinessBody({ body, formData }) {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [visibleSection, setVisibleSection] = useState(null);
  const [result, setResult] = useState(null);
  const [URL, setURL] = useState([]);
  const router = useRouter();

  const isTablet = useMediaQueryState({
    query: `(max-width: 700px)`,
  });

  useEffect(() => {
    let newURL = [...URL];
    for (let i = 0; i < body?.ai_readiness_components?.data.length; i++) {
      newURL.push(
        body?.ai_readiness_components?.data[i].attributes.heading
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll('&', 'and'),
      );
    }
    newURL.push('result');
    setURL(newURL);

    initializeStates();

    function handleHashChange() {
      let currentHash = location.hash.substring(1);

      if (
        (currentHash === newURL[newURL.length - 2] &&
          localStorage.getItem('result') !== null) ||
        (currentHash === newURL[newURL.length - 1] &&
          localStorage.getItem('result') === null)
      ) {
        handleRestart();
      } else {
        handleVisibleSection(newURL.indexOf(currentHash));
      }
    }

    addEventListener('hashchange', handleHashChange);
    return () => removeEventListener('hashchange', handleHashChange);
  }, []);

  useEffect(() => {
    let url = '';
    if (visibleSection < body?.ai_readiness_components?.data.length) {
      url = URL[visibleSection];
    } else {
      url = 'result';
    }
    router.push('#' + url, { scroll: false });

    const element = document.getElementById(body?.hero_section?.button_link);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }, [visibleSection]);

  const tag_list = body?.tag_list;
  const tag_colors = ['#FF5656', '#FF8888', '#84BD32', '#30AD43'];

  function initializeStates() {
    let newData = [];
    let newError = [];
    let newVisibleSection = 0;
    let newResult = null;

    if (localStorage.getItem('result') !== null) {
      newResult = JSON.parse(localStorage.getItem('result'));
    }

    if (
      localStorage.getItem('data') !== null &&
      localStorage.getItem('error') !== null
    ) {
      newData = JSON.parse(localStorage.getItem('data'));
      newError = JSON.parse(localStorage.getItem('error'));
    } else {
      for (let i = 0; i < body?.ai_readiness_components?.data.length; i++) {
        let arrData = [];
        let arrError = [];

        for (
          let j = 0;
          j <
          body?.ai_readiness_components?.data[i]?.attributes?.question.length;
          j++
        ) {
          if (
            body?.ai_readiness_components?.data[i]?.attributes?.question[j]
              .type === 'mcq'
          ) {
            arrData.push([null, null]);
            arrError.push(null);
          } else {
            arrData.push([
              body?.ai_readiness_components?.data[i]?.attributes?.question[j]
                .answers[0].name,
              0,
            ]);
            arrError.push(false);
          }
        }
        newData[i] = arrData;
        newError[i] = arrError;
      }
    }

    if (localStorage.getItem('visibleSection') !== null) {
      newVisibleSection = JSON.parse(localStorage.getItem('visibleSection'));
    }

    setData(newData);
    setError(newError);
    setVisibleSection(newVisibleSection);
    setResult(newResult);
  }

  function handleData(sectionIndex, questionIndex, name, value) {
    const newData = [...data];
    newData[sectionIndex][questionIndex][0] = name;
    newData[sectionIndex][questionIndex][1] = value;
    localStorage.setItem('data', JSON.stringify(newData));
    setData(newData);
  }

  function handleError(sectionIndex, questionIndex, value) {
    const newError = [...error];
    newError[sectionIndex][questionIndex] = value;
    localStorage.setItem('error', JSON.stringify(newError));
    setData(newError);
  }

  function handleVisibleSection(value) {
    if (value === -1) {
      value = 0;
    }
    localStorage.setItem('visibleSection', JSON.stringify(value));
    setVisibleSection(value);
  }

  function handlePrevious() {
    handleVisibleSection(visibleSection - 1);
  }

  function canGoToNext() {
    if (
      error[visibleSection].includes(null) ||
      error[visibleSection].includes(true)
    ) {
      let newError = [...error];
      for (let i = 0; i < newError[visibleSection].length; i++) {
        if (newError[visibleSection][i] === null) {
          newError[visibleSection][i] = true;
        }
      }
      localStorage.setItem('error', JSON.stringify(newError));
      setError(newError);
      return false;
    } else {
      return true;
    }
  }
  function handleResult() {
    if (canGoToNext()) {
      let newResult = {};
      newResult['final'] = 0;

      for (let i = 0; i < data.length; i++) {
        let weight =
          body?.ai_readiness_components?.data[i].attributes.section_weight;
        let val = 0;

        for (let j = 0; j < data[i].length; j++) {
          val = val + data[i][j][1];
        }

        newResult[i] = Math.round(val / data[i].length);
        newResult['final'] = newResult['final'] + newResult[i] * weight;
      }

      newResult['final'] = Math.round(newResult['final']);

      console.log('data', data);
      console.log('result', newResult);
      localStorage.setItem('result', JSON.stringify(newResult));
      setResult(newResult);
      return { data, newResult };
    }
  }

  function handleRestart() {
    localStorage.removeItem('data');
    localStorage.removeItem('error');
    localStorage.removeItem('visibleSection');
    localStorage.removeItem('result');
    localStorage.removeItem('subAnswer');
    router.push(
      '/ai-readiness#' +
        body?.ai_readiness_components?.data[0].attributes.heading
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll('&', 'and'),
    );
    initializeStates();
  }

  function assignTag(resultIndex) {
    let tag_name = '';
    let tag_index = 0;
    for (let i = 0; i < tag_list.length; i++) {
      if (result[resultIndex] <= tag_list[i].value) {
        tag_name = tag_list[i].name;
        tag_index = i;
        break;
      }
    }
    return [tag_name, tag_index];
  }

  function findMinSection() {
    let obj = { ...result };
    delete obj.final;
    let minKey = 0;
    let minVal = 100;
    for (let key in obj) {
      if (obj[key] < minVal) {
        minVal = obj[key];
        minKey = Number(key);
      }
    }
    return minKey;
  }

  return (
    <>
      {data && visibleSection < data.length && (
        <HeroSection heroData={body?.hero_section} variant="ai-readiness" />
      )}
      {data && (
        <div className={styles.container} id={body?.hero_section?.button_link}>
          {visibleSection < data.length && (
            <>
              {isTablet ? (
                <>
                  <div className={styles.button_wrapper_mobile}>
                    {visibleSection > 0 && visibleSection < data.length && (
                      <button onClick={handlePrevious}>
                        <Image
                          src="https://cdn.marutitech.com/black_chevron_left_5adc2eb9de.svg"
                          alt="previous section"
                          width={25}
                          height={25}
                        />
                      </button>
                    )}
                    {visibleSection + 1}/0{URL.length - 1}
                    {visibleSection < data.length - 1 && (
                      <button
                        onClick={() => {
                          if (canGoToNext())
                            handleVisibleSection(visibleSection + 1);
                        }}
                      >
                        <Image
                          src="https://cdn.marutitech.com/black_chevron_left_6d81dc24e5.svg"
                          alt="next section"
                          width={25}
                          height={25}
                        />
                      </button>
                    )}
                  </div>
                </>
              ) : (
                <AIReadinessStep
                  visibleCount={visibleSection}
                  onStepClick={stepNumber =>
                    handleVisibleSection(stepNumber - 1)
                  }
                />
              )}
            </>
          )}

          {body?.ai_readiness_components?.data.map((section, sectionIndex) => (
            <div
              key={sectionIndex}
              className={
                visibleSection === sectionIndex
                  ? styles.section_wrapper
                  : styles.hidden
              }
            >
              <div className={styles.heading}>
                <h2>
                  {sectionIndex + 1}. {section?.attributes?.heading}
                </h2>
              </div>
              {visibleSection !== data.length && (
                <QuestionAndAnswers
                  sectionIndex={sectionIndex}
                  sectionQuestions={section?.attributes.question}
                  sectionData={data[sectionIndex]}
                  sectionError={error[sectionIndex]}
                  handleData={handleData}
                  handleError={handleError}
                />
              )}
              <span id="error">
                {visibleSection < data.length &&
                  error[visibleSection].includes(true) && (
                    <div className={styles.error_message}>
                      Please fill all the required fields.
                    </div>
                  )}
              </span>

              {visibleSection === data.length - 1 && (
                <AIReadinessForm
                  formData={formData}
                  handleResult={handleResult}
                  handleVisibleSection={handleVisibleSection}
                />
              )}
              <div className={styles.button_wrapper}>
                {visibleSection > 0 && visibleSection < data.length && (
                  <button onClick={handlePrevious}>
                    <Image
                      src="https://cdn.marutitech.com/chevron_left_7f3e8fa9d6.svg"
                      alt="previous section"
                      width={50}
                      height={50}
                    />
                  </button>
                )}
                {visibleSection < data.length - 1 && (
                  <button
                    onClick={() => {
                      if (canGoToNext())
                        handleVisibleSection(visibleSection + 1);
                    }}
                  >
                    <Image
                      src="https://cdn.marutitech.com/chevron_right_0f9e1dff3c.svg"
                      alt="next section"
                      width={50}
                      height={50}
                    />
                  </button>
                )}
              </div>
            </div>
          ))}
          {visibleSection === data.length && result && (
            <div className={styles.result_section}>
              <div className={styles.button_wrapper}>
                <Button
                  className={styles.restart_button}
                  onClick={handleRestart}
                >
                  <Image
                    src="https://cdn.marutitech.com/restart_button_831deeb022.svg"
                    alt="restart assessment"
                    width={24}
                    height={24}
                  />
                  {body?.restart_button?.title}
                </Button>
              </div>
              <div className={styles.heading}>
                <h2>
                  {body?.tag?.title}:{' '}
                  <span
                    style={{
                      color: tag_colors[assignTag(findMinSection())[1]],
                    }}
                  >
                    {
                      body?.ai_readiness_components?.data[findMinSection()]
                        ?.attributes?.heading
                    }{' '}
                    : {result[findMinSection()]}%
                  </span>
                </h2>
              </div>
              <div className={styles.gauge_wrapper}>
                <RatingGauge
                  percentage={result.final}
                  tag_list={tag_list}
                  tag_color={tag_colors}
                />
              </div>
              <div className={styles.tags}>
                {tag_list.map((tag, index) => (
                  <span
                    key={index}
                    style={{
                      color: tag_colors[index],
                    }}
                  >
                    {index === 0
                      ? `<${tag_list[index].value}`
                      : index === tag_list.length - 1
                        ? `>${tag_list[index - 1].value}`
                        : `${tag_list[index - 1].value + 1} - ${tag_list[index].value}`}
                    %: {tag_list[index].name}
                    {index < tag_list.length - 1 ? ' | ' : ''}
                  </span>
                ))}
              </div>
              <div
                className={styles.description}
                dangerouslySetInnerHTML={{
                  __html: body?.tag?.description,
                }}
              />
              <div>
                <Button
                  className={styles.consultation_button}
                  label={body?.consultation_button?.title}
                  type="button"
                  onClick={() => {
                    router.push(body?.consultation_button?.link);
                  }}
                />
              </div>
              <Heading
                title={body?.score_heading}
                headingType="h2"
                className={styles.heading}
              />
              <div className={styles.score_cards_wrapper}>
                {body?.ai_readiness_components?.data.map((section, index) => (
                  <div key={index} className={styles.score_cards}>
                    <div
                      style={{
                        display: 'flex',
                        gap: '20px',
                        alignItems: 'center',
                      }}
                    >
                      <RatingProgressCircle percentage={result[index]} />
                      {result[index]}%
                    </div>
                    <div>
                      <b>{section?.attributes?.heading}</b>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
}
