import Heading from '@components/Heading';
import Image from 'next/image';
import style from './HeroSectionPodcasts.module.css';
import { Container } from 'react-bootstrap';

export default function HeroSectionPodcasts({ data }) {
  return (
    <Container fluid className={style.containerHeroSection}>
      <div className={style.content}>
        <Heading headingType="h1" title={data?.title} className={style.title} />
        <div
          className={style.description}
          dangerouslySetInnerHTML={{ __html: data?.description }}
        />
      </div>
      <div className={style.heroImageContainer}>
        <Image
          className={style.heroImage}
          src={data?.image?.data?.attributes?.url}
          alt={
            data?.image?.data?.attributes?.alternativeText ||
            data?.image?.data?.attributes?.name
          }
          width={689}
          height={681}
        />
      </div>
    </Container>
  );
}
