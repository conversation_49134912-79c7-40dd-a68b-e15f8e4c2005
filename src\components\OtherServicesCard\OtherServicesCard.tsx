'use client';

import React from 'react';
import { Container } from 'react-bootstrap';
import Image from 'next/image';
import Link from 'next/link';
import useEmblaCarousel from 'embla-carousel-react';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import useDotButton from '@hooks/useDotButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import emblastyles from '../../styles/emlaDots.module.css';

import styles from './OtherServicesCard.module.css';
import { OtherServiceCard, OtherServicesCardTypes } from './types';

export default function OtherServicesCard({
  data,
  variant = 'L2Services',
  variantWhite = true,
}: {
  data: OtherServicesCardTypes;
  variant: 'L2Services' | 'L3Services';
  variantWhite?: boolean;
}) {
  const [emblaRef, emblaApi] = useEmblaCarousel({ dragFree: true });

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const isTablet = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl']})`,
  });

  const cards =
    variant === 'L2Services'
      ? data?.other_services_card
          .slice(0, 2)
          .map((otherService: OtherServiceCard) => (
            <div className={styles.embla__slide} key={otherService?.id}>
              <Link
                href={otherService?.service_page_link}
                prefetch={false}
                className={styles.card}
                key={otherService?.id}
              >
                <div className={styles.contentWrapper}>
                  <div className={styles.cardOverlay} />
                  <Image
                    src={otherService?.on_hover_bg_image?.data?.attributes?.url}
                    fill
                    alt="onHoverBgImage"
                    className={styles.onHoverBgImage}
                  />
                  <Heading
                    headingType="h3"
                    title={otherService?.title}
                    className={styles.cardTitle}
                  />
                  <div
                    className={styles.cardDescription}
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                      __html: otherService?.description,
                    }}
                  />
                </div>
              </Link>
            </div>
          ))
      : data?.other_services_card.map((otherService: OtherServiceCard) => (
          <div className={styles.embla__slide__l3} key={otherService?.id}>
            <Link
              href={otherService?.service_page_link}
              prefetch={false}
              className={styles.card}
              key={otherService?.id}
              style={{ width: '384px' }}
            >
              <div className={styles.contentWrapper}>
                <div className={styles.cardOverlay} />
                <Image
                  src={otherService?.on_hover_bg_image?.data?.attributes?.url}
                  fill
                  alt="onHoverBgImage"
                  className={styles.onHoverBgImage}
                />
                <Heading
                  headingType="h3"
                  title={otherService?.title}
                  className={styles.cardTitle}
                />
                <div
                  className={styles.cardDescription}
                  // eslint-disable-next-line react/no-danger
                  dangerouslySetInnerHTML={{
                    __html: otherService?.description,
                  }}
                />
              </div>
            </Link>
          </div>
        ));

  return (
    <Container
      fluid
      className={classNames(
        styles.OtherServicesCardContainer,
        variant === 'L3Services' && styles.l3OtherServicesCardContainer,
      )}
    >
      <Heading
        headingType="h2"
        title={data?.title}
        className={styles.title}
        position="center"
      />
      {variant === 'L2Services' &&
        (isTablet ? (
          <div className={styles.embla}>
            <div className={styles.embla__viewport} ref={emblaRef}>
              <div className={styles.embla__container}>
                {cards}
                {variant === 'L2Services' && (
                  <div className={styles.embla__slide}>
                    <Link
                      href={data?.all_services_card_link}
                      prefetch={false}
                      className={styles.allServicesCardWrapper}
                    >
                      <div className={styles.allServicesCard}>
                        {data?.all_services_card_title}
                      </div>
                    </Link>
                  </div>
                )}
              </div>
            </div>
            <div className={styles.embla__controls}>
              <div className={emblastyles.embla__dots}>
                {scrollSnaps.map((_, index) => (
                  <DotButton
                    key={index}
                    onClick={() => onDotButtonClick(index)}
                    className={
                      index === selectedIndex
                        ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                        : variantWhite
                          ? classNames(
                              emblastyles.embla__dot,
                              emblastyles.embla__dot_bg_white,
                            )
                          : emblastyles.embla__dot
                    }
                  />
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.cardWrapper}>
            {cards}
            {variant === 'L2Services' && (
              <Link
                href={data?.all_services_card_link}
                prefetch={false}
                className={styles.allServicesCardWrapper}
              >
                <div className={styles.allServicesCard}>
                  {data?.all_services_card_title}
                </div>
              </Link>
            )}
          </div>
        ))}

      {variant === 'L3Services' && (
        <div className={styles.emblaL3}>
          <div className={styles.embla__viewport__l3} ref={emblaRef}>
            <div className={styles.embla__container__l3}>{cards}</div>
          </div>
          <div className={styles.embla__controls__l3}>
            <div className={styles.embla__dots__l3}>
              {scrollSnaps.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  className={emblastyles.embla__dot.concat(
                    index === selectedIndex
                      ? `${` ${emblastyles.embla__dot_selected}`}`
                      : '',
                  )}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </Container>
  );
}
